package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.mapper.TransactionMapper;
import com.example.payment.service.TransactionService;
import org.springframework.stereotype.Service;

/**
 * 交易记录服务实现类
 */
@Service
public class TransactionServiceImpl extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    @Override
    public void createTransaction(Order order) {

        //根据交易类型处理不同的数据
        switch (OrderTypeEnum.get)
        

        Transaction.builder()
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue())
                .balance(0L)
                .description("账户充值")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }
}
