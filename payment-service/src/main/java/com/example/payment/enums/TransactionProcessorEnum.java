package com.example.payment.enums;

import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;
import java.util.function.BiFunction;

/**
 * 交易处理器枚举
 * 将策略模式简化为枚举实现，减少类的数量
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum TransactionProcessorEnum {

    /**
     * 充值处理器
     */
    RECHARGE("recharge", "充值", (order, currentBalance) -> {
        Long newBalance = currentBalance + order.getAmount().longValue();
        log.info("处理充值交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        return Transaction.builder()
                .id(UUID.randomUUID().toString())
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue())
                .balance(newBalance)
                .description("账户充值")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }),

    /**
     * 消费处理器
     */
    CONSUME("consume", "消费", (order, currentBalance) -> {
        Long newBalance = currentBalance - order.getAmount().longValue();
        if (newBalance < 0) {
            throw new IllegalStateException("用户余额不足，无法完成消费");
        }
        log.info("处理消费交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        return Transaction.builder()
                .id(UUID.randomUUID().toString())
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(-order.getAmount().longValue()) // 消费为负数
                .balance(newBalance)
                .description("账户消费")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }),

    /**
     * VIP购买处理器
     */
    VIP("vip", "购买VIP", (order, currentBalance) -> {
        log.info("处理VIP购买交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        return Transaction.builder()
                .id(UUID.randomUUID().toString())
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue())
                .balance(currentBalance) // VIP购买不影响余额
                .description("购买VIP会员")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }),

    /**
     * 退款处理器
     */
    REFUND("refund", "退款", (order, currentBalance) -> {
        Long newBalance = currentBalance + order.getAmount().longValue();
        log.info("处理退款交易，订单ID: {}, 金额: {}", order.getId(), order.getAmount());
        
        return Transaction.builder()
                .id(UUID.randomUUID().toString())
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue())
                .balance(newBalance)
                .description("订单退款")
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    });

    /**
     * 订单类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 交易处理函数
     * 参数1: Order 订单信息
     * 参数2: Long 当前余额
     * 返回: Transaction 交易记录
     */
    private final BiFunction<Order, Long, Transaction> processor;

    /**
     * 根据订单类型代码获取处理器
     */
    public static TransactionProcessorEnum getByCode(String code) {
        for (TransactionProcessorEnum processor : values()) {
            if (processor.getCode().equals(code)) {
                return processor;
            }
        }
        return null;
    }

    /**
     * 处理交易
     */
    public Transaction processTransaction(Order order, Long currentBalance) {
        return processor.apply(order, currentBalance);
    }
}
