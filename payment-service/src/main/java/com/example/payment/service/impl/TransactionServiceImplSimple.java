package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.mapper.TransactionMapper;
import com.example.payment.service.TransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 交易记录服务实现类 - 简化版本
 * 不使用设计模式，直接优化if-else结构
 */
@Slf4j
@Service("transactionServiceSimple")
public class TransactionServiceImplSimple extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    @Override
    public void createTransaction(Order order) {
        // 使用枚举替代字符串比较，提高性能和安全性
        OrderTypeEnum orderType = OrderTypeEnum.getByCode(order.getOrderType());
        if (orderType == null) {
            throw new IllegalArgumentException("不支持的订单类型: " + order.getOrderType());
        }

        Transaction transaction = buildBaseTransaction(order);
        
        // 根据订单类型处理特定逻辑
        switch (orderType) {
            case RECHARGE:
                handleRechargeTransaction(transaction, order);
                break;
            case CONSUME:
                handleConsumeTransaction(transaction, order);
                break;
            case VIP:
                handleVipTransaction(transaction, order);
                break;
            case REFUND:
                handleRefundTransaction(transaction, order);
                break;
            default:
                throw new IllegalArgumentException("未实现的订单类型处理: " + orderType);
        }

        // 保存交易记录
        this.save(transaction);
        log.info("交易记录创建成功，订单ID: {}, 交易类型: {}", order.getId(), orderType.getName());
    }

    /**
     * 构建基础交易记录
     */
    private Transaction buildBaseTransaction(Order order) {
        return Transaction.builder()
                .id(UUID.randomUUID().toString())
                .userId(order.getUserId())
                .orderId(order.getId())
                .type(order.getOrderType())
                .amount(order.getAmount().longValue())
                .paymentMethodId(order.getPaymentMethodId())
                .status("pending")
                .build();
    }

    /**
     * 处理充值交易
     */
    private void handleRechargeTransaction(Transaction transaction, Order order) {
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        Long newBalance = currentBalance + order.getAmount().longValue();
        
        transaction.setBalance(newBalance);
        transaction.setDescription("账户充值");
        
        log.info("处理充值交易，用户ID: {}, 充值金额: {}, 新余额: {}", 
                order.getUserId(), order.getAmount(), newBalance);
    }

    /**
     * 处理消费交易
     */
    private void handleConsumeTransaction(Transaction transaction, Order order) {
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        Long newBalance = currentBalance - order.getAmount().longValue();
        
        if (newBalance < 0) {
            throw new IllegalStateException("用户余额不足，无法完成消费");
        }
        
        transaction.setAmount(-order.getAmount().longValue()); // 消费为负数
        transaction.setBalance(newBalance);
        transaction.setDescription("账户消费");
        
        log.info("处理消费交易，用户ID: {}, 消费金额: {}, 新余额: {}", 
                order.getUserId(), order.getAmount(), newBalance);
    }

    /**
     * 处理VIP购买交易
     */
    private void handleVipTransaction(Transaction transaction, Order order) {
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        
        transaction.setBalance(currentBalance); // VIP购买不影响余额
        transaction.setDescription("购买VIP会员");
        
        log.info("处理VIP购买交易，用户ID: {}, 购买金额: {}", 
                order.getUserId(), order.getAmount());
    }

    /**
     * 处理退款交易
     */
    private void handleRefundTransaction(Transaction transaction, Order order) {
        Long currentBalance = getCurrentUserBalance(order.getUserId());
        Long newBalance = currentBalance + order.getAmount().longValue();
        
        transaction.setBalance(newBalance);
        transaction.setDescription("订单退款");
        
        log.info("处理退款交易，用户ID: {}, 退款金额: {}, 新余额: {}", 
                order.getUserId(), order.getAmount(), newBalance);
    }

    /**
     * 获取用户当前余额
     * TODO: 实现用户余额查询逻辑
     */
    private Long getCurrentUserBalance(Long userId) {
        // 这里应该调用用户服务获取当前余额
        // 暂时返回模拟数据，实际项目中需要实现
        return 10000L;
    }
}
