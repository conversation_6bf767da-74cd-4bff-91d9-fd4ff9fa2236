package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.Order;
import com.example.payment.entity.Transaction;
import com.example.payment.enums.TransactionProcessorEnum;
import com.example.payment.mapper.TransactionMapper;
import com.example.payment.service.TransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 交易记录服务实现类 - 枚举策略版本
 * 使用枚举实现策略模式，代码更简洁
 */
@Slf4j
@Service("transactionServiceEnum")
public class TransactionServiceImplEnum extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    @Override
    public void createTransaction(Order order) {
        // 获取对应的处理器
        TransactionProcessorEnum processor = TransactionProcessorEnum.getByCode(order.getOrderType());
        if (processor == null) {
            throw new IllegalArgumentException("不支持的订单类型: " + order.getOrderType());
        }

        // 获取用户当前余额
        Long currentBalance = getCurrentUserBalance(order.getUserId());

        // 使用枚举策略处理交易
        Transaction transaction = processor.processTransaction(order, currentBalance);

        // 保存交易记录
        this.save(transaction);
        
        log.info("交易记录创建成功，订单ID: {}, 交易类型: {}", 
                order.getId(), processor.getName());
    }

    /**
     * 获取用户当前余额
     * TODO: 实现用户余额查询逻辑
     */
    private Long getCurrentUserBalance(Long userId) {
        // 这里应该调用用户服务获取当前余额
        // 暂时返回模拟数据，实际项目中需要实现
        return 10000L;
    }
}
